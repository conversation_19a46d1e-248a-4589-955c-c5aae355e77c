import { useQuery } from "@tanstack/react-query";

export interface Activity {
  title: string;
  description: string;
  "senpai-count": number;
  hot: boolean;
  fav: boolean;
  tags: string[];
  cover: string;
  icon: string;
}

// Mock function to simulate API call for activities
const fetchActivities = async (): Promise<Activity[]> => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 100));

  // Import the activities data
  const activitiesData = await import("../public/mocks/activities.json");
  return activitiesData.default as Activity[];
};

// Enhanced fetch function with filtering and pagination
interface FetchActivitiesOptions {
  searchValue?: string;
  showFavorites?: boolean;
  categoryFilters?: Record<string, boolean | "indeterminate">;
  page?: number;
  itemsPerPage?: number;
}

interface FetchActivitiesResult {
  activities: Activity[];
  totalItems: number;
  totalPages: number;
}

const fetchActivitiesFiltered = async (
  options: FetchActivitiesOptions = {},
): Promise<FetchActivitiesResult> => {
  const {
    searchValue = "",
    showFavorites = false,
    categoryFilters = {},
    page = 1,
    itemsPerPage = 15,
  } = options;

  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 2000));

  // Import the activities data
  const activitiesData = await import("../public/mocks/activities.json");
  const allActivities = activitiesData.default as Activity[];

  // Filter activities based on the provided options
  const filteredActivities = allActivities.filter((activity) => {
    // Search filter
    if (
      searchValue &&
      !activity.title.toLowerCase().includes(searchValue.toLowerCase())
    ) {
      return false;
    }

    // Favorites filter
    if (showFavorites && !activity.fav) {
      return false;
    }

    // Category filters (placeholder - would need actual category mapping)
    const hasActiveFilters = Object.values(categoryFilters).some(Boolean);
    if (hasActiveFilters) {
      // This would need to be implemented based on how activities are categorized
      // For now, we'll just return true
      return true;
    }

    return true;
  });

  // Pagination
  const totalItems = filteredActivities.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (page - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedActivities = filteredActivities.slice(startIndex, endIndex);

  return {
    activities: paginatedActivities,
    totalItems,
    totalPages,
  };
};

export const useActivities = () => {
  return useQuery({
    queryKey: ["activities"],
    queryFn: fetchActivities,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook for filtered activities with search, favorites, and pagination
export interface UseActivitiesFiltersOptions {
  searchValue?: string;
  showFavorites?: boolean;
  categoryFilters?: Record<string, boolean | "indeterminate">;
  page?: number;
  itemsPerPage?: number;
}

export const useActivitiesFiltered = (
  options: UseActivitiesFiltersOptions = {},
) => {
  const { page = 1, itemsPerPage = 15, ...filterOptions } = options;

  const queryResult = useQuery({
    queryKey: ["activities", "filtered", filterOptions, page, itemsPerPage],
    queryFn: () => fetchActivitiesFiltered(options),
    staleTime: 5 * 60 * 1000, // 5 minutes
    placeholderData: (previousData, previousQuery) => {
      if (!previousQuery) return undefined;

      // Compare filter options to see if only pagination changed
      const prevFilterOptions = previousQuery.queryKey[2];
      const currentFilterOptions = filterOptions;

      // If filter options are the same, it's just pagination - keep previous data
      if (
        JSON.stringify(prevFilterOptions) ===
        JSON.stringify(currentFilterOptions)
      ) {
        return previousData;
      }

      // If filters changed, don't keep previous data (allow isLoading)
      return undefined;
    },
  });

  const { data } = queryResult;

  return {
    ...queryResult,
    data: data?.activities,
    totalItems: data?.totalItems || 0,
    totalPages: data?.totalPages || 0,
    currentPage: page,
    hasNextPage: page < (data?.totalPages || 0),
    hasPreviousPage: page > 1,
  };
};

// Simple function to get total page count from activities
const fetchActivityPageCount = async (
  itemsPerPage: number = 15,
): Promise<number> => {
  // Import the activities data
  const activitiesData = await import("../public/mocks/activities.json");
  const totalItems = activitiesData.default.length;
  return Math.ceil(totalItems / itemsPerPage);
};

// Hook for getting total page count
export const useActivityPageCount = (itemsPerPage: number = 15) => {
  return useQuery({
    queryKey: ["activityPageCount", itemsPerPage],
    queryFn: () => fetchActivityPageCount(itemsPerPage),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};
